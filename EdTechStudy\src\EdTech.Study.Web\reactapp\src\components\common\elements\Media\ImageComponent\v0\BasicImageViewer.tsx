import React, { useState, useImperativeHandle, forwardRef } from 'react';

// Props cho component BasicImageViewer
export interface BasicImageViewerProps {
  imageUrl: string;
  alt: string;
  imageRef?: React.RefObject<HTMLImageElement>;
  isEditing?: boolean;
  rotation?: number;
  onRotateLeft?: () => void;
  onRotateRight?: () => void;
  onDownload?: () => void;
  showControls?: boolean;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  zoomLevel?: number;
  onZoomChange?: (scale: number) => void;
  isFullscreen?: boolean; // Add support for fullscreen mode
}

// Định nghĩa các phương thức có thể gọi từ bên ngoài
export interface BasicImageViewerRef {
  zoomIn: () => void;
  zoomOut: () => void;
  reset: () => void;
  setZoom: (scale: number) => void;
  getZoom: () => number;
}

const BasicImageViewer: React.ForwardRefRenderFunction<
  BasicImageViewerRef,
  BasicImageViewerProps
> = (
  {
    imageUrl,
    alt,
    imageRef,
    rotation = 0,
    onZoomIn,
    onZoomOut,
    zoomLevel,
    onZoomChange,
    isFullscreen = false,
  },
  ref
) => {
  // State cho zoom và vị trí
  const [scale, setScale] = useState(zoomLevel ? zoomLevel / 100 : 1);
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });

  // Update scale when zoomLevel prop changes
  React.useEffect(() => {
    if (zoomLevel) {
      setScale(zoomLevel / 100);
    }
  }, [zoomLevel]);

  // Xử lý zoom in/out
  const handleZoomIn = () => {
    const newScale = Math.min(scale + 0.25, 3);
    setScale(newScale);
    if (onZoomIn) onZoomIn();
    if (onZoomChange) onZoomChange(newScale * 100);
  };

  const handleZoomOut = () => {
    const newScale = Math.max(scale - 0.25, 0.5);
    setScale(newScale);
    if (onZoomOut) onZoomOut();
    if (onZoomChange) onZoomChange(newScale * 100);
  };

  // Set zoom to a specific level
  const handleSetZoom = (newScale: number) => {
    // Convert percentage to scale factor (0.5 to 3)
    const scaleFactor = newScale / 100;
    const clampedScale = Math.max(0.5, Math.min(scaleFactor, 3));
    setScale(clampedScale);
    if (onZoomChange) onZoomChange(clampedScale * 100);
  };

  // Reset về trạng thái ban đầu
  const handleReset = () => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
    if (onZoomChange) onZoomChange(100);
  };

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    zoomIn: handleZoomIn,
    zoomOut: handleZoomOut,
    reset: handleReset,
    setZoom: handleSetZoom,
    getZoom: () => scale * 100,
  }));

  // Xử lý sự kiện chuột
  const handleMouseDown = (e: React.MouseEvent) => {
    // Ngăn chặn các hành vi mặc định
    e.preventDefault();

    // Lưu vị trí bắt đầu kéo
    const startX = e.clientX;
    const startY = e.clientY;

    // Lưu vị trí ban đầu của ảnh
    const initialX = position.x;
    const initialY = position.y;

    // Đánh dấu đang kéo
    setIsDragging(true);

    // Hàm xử lý khi di chuyển chuột
    const handleGlobalMouseMove = (moveEvent: MouseEvent) => {
      // Ngăn chặn các hành vi mặc định
      moveEvent.preventDefault();

      // Tính toán khoảng cách di chuyển
      const dx = moveEvent.clientX - startX;
      const dy = moveEvent.clientY - startY;

      // Cập nhật vị trí mới
      setPosition({
        x: initialX + dx,
        y: initialY + dy,
      });
    };

    // Hàm xử lý khi thả chuột
    const handleGlobalMouseUp = () => {
      // Đánh dấu kết thúc kéo
      setIsDragging(false);

      // Xóa các event listeners
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
    };

    // Thêm các event listeners vào document
    document.addEventListener('mousemove', handleGlobalMouseMove);
    document.addEventListener('mouseup', handleGlobalMouseUp);
  };

  // Reset position when entering/exiting fullscreen
  React.useEffect(() => {
    if (isFullscreen) {
      // Reset position when entering fullscreen for better viewing
      setPosition({ x: 0, y: 0 });
    }
  }, [isFullscreen]);

  return (
    <div
      className="basic-image-viewer"
      style={{
        position: 'relative',
        width: '100%',
        height: isFullscreen ? '100vh' : '100%', // Use viewport height in fullscreen
        minHeight: isFullscreen ? 'auto' : '300px',
        overflow: 'hidden',
        userSelect: 'none',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f5f5f5', // Thêm màu nền để dễ nhìn
        padding: isFullscreen ? '20px' : '0', // Add padding in fullscreen mode
      }}
    >
      <div
        style={{
          position: 'relative', // Thay đổi từ absolute sang relative
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          cursor: isDragging ? 'grabbing' : 'grab',
          zIndex: 2, // Đảm bảo div này có z-index cao hơn để nhận sự kiện chuột
        }}
        onMouseDown={handleMouseDown}
      >
        <div
          style={{
            transform: `translate(${position.x}px, ${position.y}px)`,
            width: '100%', // Đảm bảo div này chiếm toàn bộ không gian có sẵn
            height: '100%', // Đảm bảo div này chiếm toàn bộ không gian có sẵn
            display: 'flex', // Sử dụng flexbox để căn giữa ảnh
            justifyContent: 'center', // Căn giữa theo chiều ngang
            alignItems: 'center', // Căn giữa theo chiều dọc
          }}
        >
          <img
            ref={imageRef}
            src={imageUrl}
            alt={alt || 'Hình ảnh'}
            style={{
              maxWidth: '100%', // Cho phép ảnh thay đổi kích thước theo container
              maxHeight: '100%', // Cho phép ảnh thay đổi kích thước theo container
              width: '100%', // Sử dụng 100% width để đảm bảo ảnh có chiều rộng bằng với container
              height: 'auto', // Tự động điều chỉnh chiều cao để giữ tỷ lệ
              objectFit: 'contain', // Đảm bảo ảnh vừa với container và giữ tỷ lệ
              transform: `scale(${scale}) rotate(${rotation}deg)`,
              transformOrigin: 'center center',
              pointerEvents: 'none', // Luôn đặt pointerEvents là 'none' để không chặn sự kiện chuột
              transition: isDragging
                ? 'none'
                : 'transform 0.2s ease, width 0.3s ease, height 0.3s ease',
              display: 'block', // Đảm bảo ảnh hiển thị dạng block
              border: '1px solid #ddd', // Thêm viền để dễ nhìn
            }}
            draggable={false}
            onLoad={(e) => {
              // Khi ảnh được tải xong, đảm bảo nó có kích thước đúng
              const img = e.target as HTMLImageElement;
              // Đảm bảo hình ảnh có chiều rộng bằng với container
              img.style.width = '100%';
              img.style.height = 'auto';
              img.style.objectFit = 'contain';

              // Log kích thước thực tế của hình ảnh để debug
              console.log(
                'Image loaded with dimensions:',
                img.naturalWidth,
                'x',
                img.naturalHeight
              );
              console.log(
                'Container dimensions:',
                img.parentElement?.offsetWidth,
                'x',
                img.parentElement?.offsetHeight
              );
            }}
          />
        </div>
      </div>

      {/* Wheel event handler for zoom */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          zIndex: 1,
          pointerEvents: 'auto', // Luôn cho phép sự kiện chuột
        }}
        onWheel={(e) => {
          e.preventDefault();
          if (e.deltaY < 0) {
            handleZoomIn();
          } else {
            handleZoomOut();
          }
        }}
      >
        {/* Debug overlay to show container dimensions */}
        <div
          style={{
            position: 'absolute',
            bottom: 5,
            right: 5,
            background: 'rgba(0,0,0,0.5)',
            color: 'white',
            padding: '2px 5px',
            fontSize: '10px',
            zIndex: 10,
            pointerEvents: 'none',
          }}
        >
          {imageUrl ? 'Image loaded' : 'No image'}
        </div>
      </div>
    </div>
  );
};

// Export với forwardRef để có thể truy cập các phương thức từ bên ngoài
export default forwardRef(BasicImageViewer);
