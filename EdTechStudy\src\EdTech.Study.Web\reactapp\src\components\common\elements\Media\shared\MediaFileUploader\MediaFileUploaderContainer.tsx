import React, { use<PERSON>allback, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, message } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { MediaFileUploader, UploadedFile } from './index';
import { MediaType } from './types';
import {
  ArrowUpOutlinedIcon,
  AudioSvg,
  BoxObjectSvg,
  ImageSvg,
  VideoSvg,
} from '../../../../../icons/IconRegister';

interface MediaFileUploaderContainerProps {
  /**
   * Type of media to upload (image, video, audio, model3d)
   */
  mediaType: MediaType;

  /**
   * Callback when upload is successful
   */
  onUploadSuccess: (files: UploadedFile[]) => void;

  /**
   * Whether to allow drag and drop
   */
  allowDrop?: boolean;

  /**
   * Height of the drop area
   */
  dropAreaHeight?: string | number;

  /**
   * Width of the drop area
   */
  dropAreaWidth?: string | number;

  /**
   * Additional CSS class for the container
   */
  className?: string;

  /**
   * Additional inline styles
   */
  style?: React.CSSProperties;

  /**
   * Whether the component is in editing mode
   */
  isEditing?: boolean;

  /**
   * Callback when upload is complete
   */
  onUploadComplete?: () => void;

  /**
   * Whether to allow multiple file uploads
   */
  multiple?: boolean;

  /**
   * Reference to the container element
   */
  containerRef?: React.RefObject<HTMLDivElement>;

  /**
   * Additional upload props
   */
  uploadProps?: any;
}

/**
 * A container component that wraps MediaFileUploader with consistent styling and behavior
 * for use across different media components (image, video, audio, model3d)
 */
const MediaFileUploaderContainer: React.FC<MediaFileUploaderContainerProps> = ({
  mediaType,
  onUploadSuccess,
  allowDrop = true,
  dropAreaHeight = '200px',
  dropAreaWidth = '100%',
  className = '',
  style = {},
  isEditing = true,
  onUploadComplete,
  multiple = true,
  containerRef,
  uploadProps = {},
}) => {
  // State for tracking upload status
  const [isUploading, setIsUploading] = useState<boolean>(false);

  // Get the appropriate text based on media type
  const getMediaTypeText = () => {
    switch (mediaType) {
      case 'image':
        return 'hình ảnh';
      case 'video':
        return 'video';
      case 'audio':
        return 'âm thanh';
      case 'model3d':
        return 'mô hình 3D';
      default:
        return 'tệp';
    }
  };

  // If not in edit mode, don't render the uploader
  if (!isEditing) {
    return null; // Không hiển thị gì khi không ở chế độ chỉnh sửa
  }

  // Handle upload start
  const handleUploadStart = useCallback(() => {
    setIsUploading(true);
  }, []);

  // Handle upload complete
  const handleUploadComplete = useCallback(() => {
    setIsUploading(false);
    onUploadComplete?.();
  }, []);

  // Handle upload error
  const handleUploadError = useCallback((error: any) => {
    setIsUploading(false);
    message.error({
      content: `Không thể tải ${getMediaTypeText()} lên. Vui lòng thử lại.`,
      key: 'uploadMessage',
      duration: 3,
    });
    console.error('Upload error:', error);
  }, []);

  // Get the appropriate icon based on media type
  const getMediaIcon = () => {
    switch (mediaType) {
      case 'image':
        return <ImageSvg />;
      case 'video':
        return <VideoSvg />;
      case 'audio':
        return <AudioSvg />;
      case 'model3d':
        return <BoxObjectSvg />;
      default:
        return <ArrowUpOutlinedIcon className="tailwind-text-4xl" />;
    }
  };

  // Get the appropriate file type hint based on media type
  const getFileTypeHint = () => {
    switch (mediaType) {
      case 'image':
        return 'Hỗ trợ: JPG, PNG, GIF, SVG';
      case 'video':
        return 'Hỗ trợ: MP4, WebM, MOV';
      case 'audio':
        return 'Hỗ trợ: MP3, WAV, OGG, M4A';
      case 'model3d':
        return 'Hỗ trợ: GLB, OBJ';
      default:
        return '';
    }
  };

  // Get the appropriate file accept pattern based on media type
  const getAcceptPattern = () => {
    switch (mediaType) {
      case 'image':
        return 'image/*,.jpg,.jpeg,.png,.gif,.svg';
      case 'video':
        return 'video/*,.mp4,.webm,.mov';
      case 'audio':
        return 'audio/*,.mp3,.wav,.ogg,.m4a';
      case 'model3d':
        return '.glb,.obj';
      default:
        return '*/*';
    }
  };

  // Render the upload button
  const renderUploadButton = () => {
    return (
      <Button
        size="middle"
        icon={
          isUploading ? (
            <LoadingOutlined />
          ) : (
            <ArrowUpOutlinedIcon height={14} width={14} />
          )
        }
        style={{
          backgroundColor: 'var(--edtt-color-primary)',
          color: 'var(--edtt-color-white)',
          border: 'none',
          borderRadius: '4px',
        }}
        disabled={isUploading}
      >
        {isUploading
          ? `Đang tải ${getMediaTypeText()}...`
          : `Tải ${getMediaTypeText()} lên`}
      </Button>
    );
  };

  // If we're just showing the upload button (no drag & drop)
  if (!allowDrop) {
    return (
      <MediaFileUploader
        mediaType={mediaType}
        onUploadSuccess={onUploadSuccess}
        allowDrop={false}
        multiple={multiple}
        uploadButtonContent={renderUploadButton()}
        onUploadStart={handleUploadStart}
        onUploadComplete={handleUploadComplete}
        onUploadError={handleUploadError}
        uploadProps={{
          accept: getAcceptPattern(),
          ...uploadProps,
        }}
      />
    );
  }

  // Render the full drag & drop area
  const dropAreaContent = (
    <div className="tailwind-flex tailwind-w-full tailwind-flex-col tailwind-items-center tailwind-justify-center tailwind-py-4">
      <p className="tailwind-text-primary">{getMediaIcon()}</p>
      <p className="tailwind-mt-2 tailwind-font-medium tailwind-text-sm tailwind-text-center">
        Kéo & thả {getMediaTypeText()} hoặc nhấp để tải lên
      </p>
      <p className="tailwind-text-xs tailwind-text-gray-500 tailwind-text-center tailwind-px-2 tailwind-mt-1">
        {getFileTypeHint()}
      </p>
      <div className="tailwind-mt-4">
        <MediaFileUploader
          mediaType={mediaType}
          onUploadSuccess={onUploadSuccess}
          allowDrop={false}
          multiple={multiple}
          uploadButtonContent={renderUploadButton()}
          onUploadStart={handleUploadStart}
          onUploadComplete={handleUploadComplete}
          onUploadError={handleUploadError}
          uploadProps={{
            accept: getAcceptPattern(),
            ...uploadProps,
          }}
        />
      </div>
    </div>
  );

  return (
    <div
      ref={containerRef}
      className={`tailwind-relative tailwind-flex tailwind-flex-col tailwind-items-center tailwind-w-full tailwind-max-w-full tailwind-min-h-[200px] media-file-uploader-container ${
        isEditing ? 'media-container editing' : ''
      }`}
    >
      {isUploading && (
        <div className="tailwind-absolute tailwind-inset-0 tailwind-bg-white tailwind-bg-opacity-70 tailwind-z-10 tailwind-flex tailwind-items-center tailwind-justify-center">
          <Spin
            indicator={
              <LoadingOutlined
                style={{ fontSize: 32, color: 'var(--edtt-color-primary)' }}
                spin
              />
            }
            tip={`Đang tải ${getMediaTypeText()} lên...`}
          />
        </div>
      )}
      <MediaFileUploader
        mediaType={mediaType}
        onUploadSuccess={onUploadSuccess}
        allowDrop={true}
        dropAreaHeight={dropAreaHeight}
        dropAreaWidth={dropAreaWidth}
        className={`${isEditing ? 'editing' : ''} ${className}`}
        style={{
          backgroundColor: 'var(--edtt-color-primary-100)',
          ...style,
        }}
        onUploadStart={handleUploadStart}
        onUploadComplete={handleUploadComplete}
        onUploadError={handleUploadError}
        multiple={multiple}
        dropAreaContent={dropAreaContent}
        uploadProps={{
          accept: getAcceptPattern(),
          ...uploadProps,
        }}
      />
    </div>
  );
};

export default MediaFileUploaderContainer;
