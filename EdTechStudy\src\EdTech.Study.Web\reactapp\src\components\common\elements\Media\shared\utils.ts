import { appConfig } from '../../../../../constants/AppContants';

/**
 * Process a URL to ensure it's properly formatted for loading resources
 * @param url The input URL to process
 * @param options Optional configuration options
 * @returns The processed URL
 */
export const processResourceUrl = (
  url: string,
  options: {
    addOrigin?: boolean;
    encodeSpecialChars?: boolean;
    corsProxy?: string;
    forceHttps?: boolean;
  } = {
    addOrigin: true,
    encodeSpecialChars: true,
    corsProxy: '',
    forceHttps: false,
  }
): string => {
  if (!url) return url;

  let processedUrl = url;

  // Handle relative URLs - ensure they start with a slash
  if (
    !processedUrl.startsWith('http') &&
    !processedUrl.startsWith('data:') &&
    !processedUrl.startsWith('blob:') &&
    !processedUrl.startsWith('/')
  ) {
    processedUrl = '/' + processedUrl;
  }

  // Add origin for relative URLs if needed
  if (
    options.addOrigin &&
    !processedUrl.startsWith('http') &&
    !processedUrl.startsWith('data:') &&
    !processedUrl.startsWith('blob:')
  ) {
    // Use our constant for the base URL instead of window.location.origin
    processedUrl = appConfig.baseURL + processedUrl;
  }

  // Force HTTPS if requested
  if (options.forceHttps && processedUrl.startsWith('http:')) {
    processedUrl = processedUrl.replace(/^http:/, 'https:');
  }

  // Handle URL encoding for special characters if needed
  if (
    options.encodeSpecialChars &&
    (processedUrl.includes(' ') || /[^\x00-\x7F]/.test(processedUrl))
  ) {
    try {
      const urlObj = new URL(processedUrl);
      processedUrl = urlObj.toString();
    } catch (error) {
      // Try to encode manually if URL constructor fails
      processedUrl = encodeURI(processedUrl);
    }
  }

  // Add CORS proxy if provided
  if (options.corsProxy && processedUrl.startsWith('http')) {
    processedUrl = `${options.corsProxy}${encodeURIComponent(processedUrl)}`;
  }

  return processedUrl;
};

/**
 * Process a media URL for any media type (image, video, audio, 3D model)
 * @param url The input URL to process
 * @param mediaType Optional media type for specific processing
 * @returns An object containing the full URL and original URL
 */
export const processMediaUrl = (
  url: string,
  mediaType?: 'image' | 'video' | 'audio' | 'model3d'
): { fullUrl: string; originalUrl: string } => {
  // Ensure URL starts with a slash if it's a relative URL
  let mediaUrl = url;
  if (
    mediaUrl &&
    !mediaUrl.startsWith('/') &&
    !mediaUrl.startsWith('http') &&
    !mediaUrl.startsWith('data:') &&
    !mediaUrl.startsWith('blob:')
  ) {
    mediaUrl = '/' + mediaUrl;
  }

  // Process the URL with our utility function
  const fullUrl = processResourceUrl(mediaUrl, {
    addOrigin: true,
    encodeSpecialChars: true,
    // Force HTTPS for video and audio to avoid mixed content issues
    forceHttps: mediaType === 'video' || mediaType === 'audio',
  });

  return { fullUrl, originalUrl: mediaUrl };
};

/**
 * Process a model URL (alias for processMediaUrl with model3d type)
 * @param url The input URL to process
 * @returns An object containing the full URL and original URL
 */
export const processModelUrl = (
  url: string
): { fullUrl: string; originalUrl: string } => {
  return processMediaUrl(url, 'model3d');
};

/**
 * Process an image URL (alias for processMediaUrl with image type)
 * @param url The input URL to process
 * @returns An object containing the full URL and original URL
 */
export const processImageUrl = (
  url: string
): { fullUrl: string; originalUrl: string } => {
  return processMediaUrl(url, 'image');
};

/**
 * Process a video URL (alias for processMediaUrl with video type)
 * @param url The input URL to process
 * @returns An object containing the full URL and original URL
 */
export const processVideoUrl = (
  url: string
): { fullUrl: string; originalUrl: string } => {
  return processMediaUrl(url, 'video');
};

/**
 * Process an audio URL (alias for processMediaUrl with audio type)
 * @param url The input URL to process
 * @returns An object containing the full URL and original URL
 */
export const processAudioUrl = (
  url: string
): { fullUrl: string; originalUrl: string } => {
  return processMediaUrl(url, 'audio');
};

/**
 * Check if a URL is likely to have CORS issues
 * @param url The URL to check
 * @returns True if the URL might have CORS issues
 */
export const mightHaveCorsIssues = (url: string): boolean => {
  if (!url || !url.startsWith('http')) return false;

  try {
    const urlObj = new URL(url);
    const urlOrigin = urlObj.origin;

    // Check if the URL origin matches our API base URL
    return !urlOrigin.startsWith(appConfig.baseURL);
  } catch (error) {
    return false;
  }
};

/**
 * Extract file extension from a URL or filename
 * @param url The URL or filename to analyze
 * @returns The file extension without the dot, or empty string if none found
 */
export const getFileExtension = (url: string): string => {
  if (!url) return '';

  // Remove query parameters and hash
  const cleanUrl = url.split(/[?#]/)[0];

  // Get the last part after the last dot
  const extension = cleanUrl.split('.').pop()?.toLowerCase() || '';

  // If the entire string was returned (no dots in the name), return empty string
  if (extension === cleanUrl.toLowerCase()) {
    return '';
  }

  return extension;
};

/**
 * Get file name without extension from a URL or filename
 * @param url The URL or filename to analyze
 * @returns The filename without extension
 */
export const getFileNameWithoutExtension = (url: string): string => {
  if (!url) return '';

  // Get the filename from the URL (last part after the last slash)
  const filename = url.split('/').pop() || '';

  // Remove the extension
  const lastDotIndex = filename.lastIndexOf('.');
  return lastDotIndex > 0 ? filename.substring(0, lastDotIndex) : filename;
};

/**
 * Guess the format of a 3D model from its URL
 * @param url The URL to analyze
 * @returns The guessed format or null if can't determine
 */
export const guessFormatFromUrl = (
  url: string
): 'obj' | 'glb' | 'fbx' | null => {
  const extension = getFileExtension(url);

  // Check for known extensions
  if (extension === 'obj') return 'obj';
  if (extension === 'glb') return 'glb';
  if (extension === 'fbx') return 'fbx';

  // Try to guess from URL patterns
  const lowerUrl = url.toLowerCase();
  if (lowerUrl.includes('/obj/') || lowerUrl.includes('_obj_')) return 'obj';
  if (lowerUrl.includes('/glb/') || lowerUrl.includes('_glb_')) return 'glb';
  if (lowerUrl.includes('/fbx/') || lowerUrl.includes('_fbx_')) return 'fbx';

  // Can't determine format
  return null;
};

/**
 * Extract YouTube video ID from a YouTube URL
 * @param url The YouTube URL
 * @returns The YouTube video ID or empty string if not found
 */
export const extractYoutubeId = (url: string): string => {
  const regExp =
    /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
  const match = url.match(regExp);
  return match && match[2].length === 11 ? match[2] : '';
};

/**
 * Extract Vimeo video ID from a Vimeo URL
 * @param url The Vimeo URL
 * @returns The Vimeo video ID or empty string if not found
 */
export const extractVimeoId = (url: string): string => {
  const regExp =
    /vimeo\.com\/(?:channels\/(?:\w+\/)?|groups\/(?:[^\/]*)\/videos\/|album\/(?:\d+)\/video\/|)(\d+)(?:$|\/|\?)/;
  const match = url.match(regExp);
  return match ? match[1] : '';
};

// ========================================
// BASE64 JS HANDLING FUNCTIONS
// ========================================

/**
 * Convert a file to base64 string
 * @param file The file to convert
 * @returns Promise<string> The base64 string
 */
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        // Remove the data URL prefix (e.g., "data:application/octet-stream;base64,")
        const base64 = reader.result.split(',')[1];
        resolve(base64);
      } else {
        reject(new Error('Failed to convert file to base64'));
      }
    };
    reader.onerror = () => reject(new Error('Error reading file'));
    reader.readAsDataURL(file);
  });
};

/**
 * Create enhanced base64 JavaScript content with executable functions
 * This creates a more robust JS file that can be executed to extract base64 data
 * @param fileName The original file name
 * @param base64Content The base64 content
 * @param originalFile The original file for metadata
 * @returns The JavaScript content as string
 */
export const createBase64JSContent = (
  fileName: string,
  base64Content: string,
  originalFile: File
): string => {
  const jsContent = `
// Generated base64 JS file for 3D model: ${fileName}
// Original file size: ${originalFile.size} bytes
// Generated at: ${new Date().toISOString()}

const MODEL_DATA = {
  fileName: "${fileName}",
  fileSize: ${originalFile.size},
  mimeType: "${originalFile.type || 'application/octet-stream'}",
  base64: "${base64Content}"
};

// Function to get the base64 data
function getModelBase64() {
  return MODEL_DATA.base64;
}

// Function to get model metadata
function getModelMetadata() {
  return {
    fileName: MODEL_DATA.fileName,
    fileSize: MODEL_DATA.fileSize,
    mimeType: MODEL_DATA.mimeType
  };
}

// Function to convert base64 to blob
function convertToBlob() {
  try {
    const binaryString = atob(MODEL_DATA.base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return new Blob([bytes], { type: MODEL_DATA.mimeType });
  } catch (error) {
    console.error('Error converting base64 to blob:', error);
    throw new Error('Failed to convert base64 to blob');
  }
}

// Function to create blob URL
function createBlobUrl() {
  try {
    const blob = convertToBlob();
    return URL.createObjectURL(blob);
  } catch (error) {
    console.error('Error creating blob URL:', error);
    throw new Error('Failed to create blob URL');
  }
}

// Export functions for external use
if (typeof window !== 'undefined') {
  window.MODEL_DATA = MODEL_DATA;
  window.getModelBase64 = getModelBase64;
  window.getModelMetadata = getModelMetadata;
  window.convertToBlob = convertToBlob;
  window.createBlobUrl = createBlobUrl;
}

// For module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    MODEL_DATA,
    getModelBase64,
    getModelMetadata,
    convertToBlob,
    createBlobUrl
  };
}

// Legacy support - for backward compatibility with existing code
const ${fileName.replace(/[^a-zA-Z0-9]/g, '_')}_base64 = "${base64Content}";
if (typeof window !== 'undefined') {
  window.${fileName.replace(/[^a-zA-Z0-9]/g, '_')}_base64 = ${fileName.replace(
    /[^a-zA-Z0-9]/g,
    '_'
  )}_base64;
}
`;

  return jsContent;
};

/**
 * Create and download a JavaScript file with base64 content
 * @param fileName The original file name
 * @param base64Content The base64 content
 * @param originalFile The original file for metadata
 */
export const createAndDownloadBase64JSFile = (
  fileName: string,
  base64Content: string,
  originalFile: File
) => {
  // Get file name without extension
  const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '');

  // Create JavaScript content using the shared function
  const jsContent = createBase64JSContent(
    fileName,
    base64Content,
    originalFile
  );

  // Create blob and download
  const blob = new Blob([jsContent], { type: 'application/javascript' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `${nameWithoutExt}.b64.js`;

  // Trigger download
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  // Clean up
  URL.revokeObjectURL(url);
};

/**
 * Check if a URL points to a base64 JS file
 * @param url The URL to check
 * @returns boolean
 */
export const isBase64JSFile = (url: string): boolean => {
  if (!url || typeof url !== 'string') {
    return false;
  }

  // Check if the URL ends with .b64.js or contains base64 indicators
  return (
    url.includes('.b64.js') ||
    url.includes('base64.js') ||
    (url.includes('.js') && url.includes('base64'))
  );
};

/**
 * Convert base64 string to blob URL
 * @param base64Content The base64 content
 * @param mimeType The MIME type of the original file
 * @returns string The blob URL
 */
export const base64ToBlobUrl = (
  base64Content: string,
  mimeType: string = 'application/octet-stream'
): string => {
  try {
    // Convert base64 to binary
    const binaryString = atob(base64Content);
    const bytes = new Uint8Array(binaryString.length);

    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    // Create blob and return URL
    const blob = new Blob([bytes], { type: mimeType });
    return URL.createObjectURL(blob);
  } catch (error) {
    console.error('Error converting base64 to blob URL:', error);
    throw new Error('Failed to convert base64 to blob URL');
  }
};

/**
 * Load and convert base64 JS file to blob URL using function execution
 * This is the primary method that executes the JS file to get base64 data
 * @param url The URL of the base64 JS file
 * @returns Promise<string> The blob URL
 */
export const loadBase64JSFileAndConvertToBlobUrl = async (
  url: string
): Promise<string> => {
  try {
    console.log('Loading base64 JS file:', url);

    // Fetch the JS file
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const jsContent = await response.text();
    console.log('JS file loaded, size:', jsContent.length);

    // Method 1: Try to execute JS and get blob URL directly
    try {
      const blobUrl = await executeJSFileAndGetBlobUrl(jsContent);
      console.log('Successfully executed JS and got blob URL:', blobUrl);
      return blobUrl;
    } catch (executionError) {
      console.warn(
        'JS execution method failed, trying fallback:',
        executionError
      );

      // Method 2: Fallback to regex extraction
      return await extractBase64FromJSContent(jsContent);
    }
  } catch (error) {
    console.error('Error loading base64 JS file:', error);
    throw new Error(
      `Failed to load base64 JS file: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
    );
  }
};

/**
 * Execute JS file content and get blob URL using the embedded functions
 * @param jsContent The JavaScript content
 * @returns Promise<string> The blob URL
 */
const executeJSFileAndGetBlobUrl = async (
  jsContent: string
): Promise<string> => {
  return new Promise((resolve, reject) => {
    try {
      // Create a temporary script element to execute the JS
      const script = document.createElement('script');
      script.type = 'text/javascript';

      // Wrap the JS content in a function to avoid global scope pollution
      const wrappedContent = `
        (function() {
          try {
            ${jsContent}
            
            // Check if createBlobUrl function exists and call it
            if (typeof createBlobUrl === 'function') {
              return createBlobUrl();
            } else if (typeof window !== 'undefined' && typeof window.createBlobUrl === 'function') {
              return window.createBlobUrl();
            } else {
              throw new Error('createBlobUrl function not found');
            }
          } catch (error) {
            throw error;
          }
        })();
      `;

      // Execute the script and get the result
      const scriptFunction = new Function('return ' + wrappedContent);
      const blobUrl = scriptFunction();

      if (!blobUrl || !blobUrl.startsWith('blob:')) {
        throw new Error('Invalid blob URL returned from JS execution');
      }

      resolve(blobUrl);
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Fallback method: Extract base64 from JS content using regex
 * @param jsContent The JavaScript content
 * @returns Promise<string> The blob URL
 */
const extractBase64FromJSContent = async (
  jsContent: string
): Promise<string> => {
  try {
    console.log('Using fallback regex extraction method');

    // Method 1: Try to extract from MODEL_DATA structure
    let base64Match = jsContent.match(/base64:\s*"([^"]+)"/);
    let mimeTypeMatch = jsContent.match(/mimeType:\s*"([^"]+)"/);

    // Method 2: Try legacy pattern if MODEL_DATA not found
    if (!base64Match) {
      base64Match = jsContent.match(/const\s+\w+_base64\s*=\s*"([^"]+)"/);
    }

    // Method 3: Try to find any base64 string (fallback)
    if (!base64Match) {
      // Look for any long base64-like string
      base64Match = jsContent.match(/"([A-Za-z0-9+\/]{100,}={0,2})"/);
    }

    if (!base64Match || !base64Match[1]) {
      throw new Error('Could not extract base64 content from JS file');
    }

    const base64Content = base64Match[1];
    console.log('Extracted base64 content length:', base64Content.length);

    // Extract MIME type
    const mimeType = mimeTypeMatch
      ? mimeTypeMatch[1]
      : 'application/octet-stream';
    console.log('Detected MIME type:', mimeType);

    // Convert to blob URL
    const blobUrl = base64ToBlobUrl(base64Content, mimeType);
    console.log('Created blob URL using fallback method:', blobUrl);

    return blobUrl;
  } catch (error) {
    console.error('Error in fallback extraction:', error);
    throw error;
  }
};

/**
 * Alternative method using eval (less secure but more compatible)
 * Use this as a last resort if the primary method fails
 * @param url The URL of the base64 JS file
 * @returns Promise<string> The blob URL
 */
export const loadBase64JSFileAndConvertToBlobUrlUsingEval = async (
  url: string
): Promise<string> => {
  try {
    console.log('Loading base64 JS file with eval method:', url);

    // Fetch the JS file
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const jsContent = await response.text();
    console.log('JS file loaded, size:', jsContent.length);

    // Create a safe execution context
    const context: any = {
      console: console,
      atob: atob,
      Uint8Array: Uint8Array,
      Blob: Blob,
      URL: URL,
    };

    // Execute the JS content in the context
    const executeInContext = new Function(
      'context',
      `
      with (context) {
        ${jsContent}
        
        // Try to get blob URL
        if (typeof createBlobUrl === 'function') {
          return createBlobUrl();
        } else if (typeof MODEL_DATA !== 'undefined' && MODEL_DATA.base64) {
          // Manual conversion
          const binaryString = atob(MODEL_DATA.base64);
          const bytes = new Uint8Array(binaryString.length);
          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
          }
          const blob = new Blob([bytes], { type: MODEL_DATA.mimeType || 'application/octet-stream' });
          return URL.createObjectURL(blob);
        } else {
          throw new Error('Could not extract or convert base64 data');
        }
      }
    `
    );

    const blobUrl = executeInContext(context);
    console.log('Blob URL created with eval method:', blobUrl);

    if (!blobUrl || !blobUrl.startsWith('blob:')) {
      throw new Error('Invalid blob URL returned');
    }

    return blobUrl;
  } catch (error) {
    console.error('Error loading base64 JS file with eval method:', error);
    throw new Error(
      `Failed to load base64 JS file: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
    );
  }
};

/**
 * Cleanup blob URLs to prevent memory leaks
 * @param blobUrl The blob URL to cleanup
 */
export const cleanupBlobUrl = (blobUrl: string): void => {
  if (blobUrl && blobUrl.startsWith('blob:')) {
    try {
      URL.revokeObjectURL(blobUrl);
      console.log('Blob URL cleaned up:', blobUrl);
    } catch (error) {
      console.error('Error cleaning up blob URL:', error);
    }
  }
};

/**
 * Process 3D model files to base64 and create JS files
 * @param files Array of 3D model files
 * @returns Promise<void>
 */
export const process3DFilesToBase64JS = async (
  files: File[]
): Promise<void> => {
  for (const file of files) {
    try {
      const base64Content = await fileToBase64(file);
      createAndDownloadBase64JSFile(file.name, base64Content, file);
    } catch (error) {
      console.error(`Error processing file ${file.name}:`, error);
      throw error;
    }
  }
};
